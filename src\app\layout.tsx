import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { Providers } from "@/components/providers/session-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Nihongo Master - AI-Powered Japanese Learning",
  description: "Master Japanese with personalized AI-generated roadmaps and lessons. From beginner to advanced, learn at your own pace.",
  keywords: ["Japanese", "learning", "AI", "Nihongo", "JLPT", "language", "education"],
  authors: [{ name: "Nihongo Master Team" }],
  openGraph: {
    title: "Nihongo Master - AI-Powered Japanese Learning",
    description: "Master Japanese with personalized AI-generated roadmaps and lessons",
    url: "https://nihongo-master.com",
    siteName: "Nihongo Master",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Nihongo Master - AI-Powered Japanese Learning",
    description: "Master Japanese with personalized AI-generated roadmaps and lessons",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
