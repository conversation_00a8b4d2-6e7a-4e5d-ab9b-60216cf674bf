import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const roadmaps = await db.roadmap.findMany({
      where: { userId: session.user.id },
      include: {
        modules: {
          include: {
            milestones: true,
            lessons: {
              include: {
                progress: {
                  where: { userId: session.user.id }
                }
              },
              orderBy: { order: 'asc' }
            },
            progress: {
              where: { userId: session.user.id }
            }
          },
          orderBy: { order: 'asc' }
        },
        progress: {
          where: { userId: session.user.id }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({ roadmaps })
  } catch (error) {
    console.error("Error fetching roadmaps:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}