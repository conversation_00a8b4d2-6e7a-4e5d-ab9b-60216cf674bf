import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { SpacedRepetitionScheduler } from "@/lib/spaced-repetition"
import { ReviewType } from "@prisma/client"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { reviewType = "MIXED", maxItems } = await request.json()

    // Get user's lesson history
    const lessonHistory = await db.lessonHistory.findMany({
      where: { userId: session.user.id },
      include: {
        lesson: {
          include: {
            module: {
              include: {
                roadmap: true
              }
            }
          }
        }
      },
      orderBy: { completedAt: 'desc' },
      take: 50 // Limit to recent lessons for performance
    })

    // Get existing review items
    const existingReviewItems = await db.reviewItem.findMany({
      where: {
        lesson: {
          module: {
            roadmap: {
              userId: session.user.id
            }
          }
        }
      },
      include: {
        lesson: true
      }
    })

    // Convert to format expected by scheduler
    const formattedHistory = lessonHistory.map(history => ({
      lessonId: history.lessonId,
      score: history.score,
      completedAt: history.completedAt,
      answers: history.answers
    }))

    const formattedExistingItems = existingReviewItems.map(item => ({
      lessonId: item.lessonId,
      itemType: item.itemType,
      difficulty: item.difficulty,
      interval: item.interval,
      easeFactor: item.easeFactor,
      nextReview: item.nextReview
    }))

    // Generate review items
    const reviewItems = SpacedRepetitionScheduler.generateReviewItems(
      formattedHistory,
      formattedExistingItems
    )

    // Filter by review type if specified
    let filteredItems = reviewItems
    if (reviewType !== "MIXED") {
      const typeMap = {
        VOCABULARY: "VOCABULARY",
        GRAMMAR: "GRAMMAR_POINT", 
        KANJI: "KANJI_CHARACTER"
      }
      filteredItems = reviewItems.filter(item => 
        item.type === typeMap[reviewType as keyof typeof typeMap]
      )
    }

    // Get due items and prioritize them
    const dueItems = SpacedRepetitionScheduler.getDueReviewItems(filteredItems)
    const prioritizedItems = SpacedRepetitionScheduler.prioritizeReviewItems(dueItems)

    // Limit items based on user preference or optimal calculation
    const user = await db.user.findUnique({
      where: { id: session.user.id }
    })

    const optimalSize = user?.timeCommitment 
      ? SpacedRepetitionScheduler.calculateOptimalSessionSize(user.timeCommitment)
      : 10

    const sessionSize = Math.min(
      maxItems || optimalSize,
      prioritizedItems.length
    )

    const selectedItems = prioritizedItems.slice(0, sessionSize)

    if (selectedItems.length === 0) {
      return NextResponse.json({ 
        message: "No items due for review",
        reviewItems: [],
        stats: SpacedRepetitionScheduler.generateReviewStats(reviewItems)
      })
    }

    // Create review session
    const reviewSession = await db.reviewSession.create({
      data: {
        userId: session.user.id,
        type: reviewType as ReviewType,
        items: selectedItems.length,
        score: 0, // Will be updated when completed
        timeSpent: 0, // Will be updated when completed
        completedAt: new Date()
      }
    })

    // Create review items for the session
    await db.reviewItem.createMany({
      data: selectedItems.map(item => ({
        sessionId: reviewSession.id,
        lessonId: item.id,
        itemType: item.type,
        difficulty: item.difficulty,
        nextReview: item.nextReview,
        interval: item.interval,
        easeFactor: item.easeFactor
      }))
    })

    // Fetch the created review items with lesson details
    const createdReviewItems = await db.reviewItem.findMany({
      where: { sessionId: reviewSession.id },
      include: {
        lesson: {
          include: {
            module: {
              include: {
                roadmap: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({ 
      reviewSession,
      reviewItems: createdReviewItems,
      stats: SpacedRepetitionScheduler.generateReviewStats(reviewItems)
    })

  } catch (error) {
    console.error("Review generation error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}