import { UserLevel } from "@prisma/client"
import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      level: UserLevel
      learningGoals: string
      timeCommitment: number
    }
  }

  interface User {
    id: string
    email: string
    name?: string | null
    level: UserLevel
    learningGoals: string
    timeCommitment: number
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    level: UserLevel
    learningGoals: string
    timeCommitment: number
  }
}