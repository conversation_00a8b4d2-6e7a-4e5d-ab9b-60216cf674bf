"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { CheckCircle, Clock, BookOpen, ArrowLeft, ArrowRight, Trophy } from "lucide-react"
import Link from "next/link"

interface LessonContent {
  explanations: Array<{
    title: string
    content: string
    examples?: Array<{
      japanese: string
      reading: string
      meaning: string
    }>
  }>
  culturalNotes?: Array<{
    title: string
    content: string
  }>
}

interface LessonExercises {
  vocabulary?: Array<{
    word: string
    reading: string
    meaning: string
    type: string
  }>
  grammar?: Array<{
    pattern: string
    meaning: string
    examples: Array<{
      japanese: string
      reading: string
      meaning: string
    }>
    practice: Array<{
      question: string
      answer: string
    }>
  }>
  kanji?: Array<{
    character: string
    meaning: string
    readings: Array<{
      type: string
      reading: string
    }>
    strokeCount: number
    examples: Array<{
      word: string
      reading: string
      meaning: string
    }>
  }>
  matching?: Array<{
    question: string
    options: string[]
    correctAnswer: string
  }>
  fillInBlanks?: Array<{
    sentence: string
    answer: string
    options?: string[]
  }>
}

interface Lesson {
  id: string
  title: string
  description: string
  content: string
  exercises: string
  order: number
  module: {
    id: string
    title: string
    type: string
    roadmap: {
      id: string
      title: string
    }
  }
  progress?: Array<{
    status: string
    progress: number
  }>
  history?: Array<{
    score: number
    timeSpent: number
    completedAt: string
  }>
}

interface UserAnswers {
  [key: string]: string
}

export default function LessonPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const lessonId = searchParams.get("id")
  
  const [lesson, setLesson] = useState<Lesson | null>(null)
  const [lessonContent, setLessonContent] = useState<LessonContent | null>(null)
  const [lessonExercises, setLessonExercises] = useState<LessonExercises | null>(null)
  const [userAnswers, setUserAnswers] = useState<UserAnswers>({})
  const [currentTab, setCurrentTab] = useState("content")
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [startTime, setStartTime] = useState<Date | null>(null)
  const [showResults, setShowResults] = useState(false)
  const [score, setScore] = useState(0)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
    }
  }, [status, router])

  useEffect(() => {
    if (lessonId && session) {
      fetchLesson()
    }
  }, [lessonId, session])

  const fetchLesson = async () => {
    try {
      const response = await fetch(`/api/lessons/${lessonId}`)
      if (response.ok) {
        const data = await response.json()
        setLesson(data.lesson)
        setLessonContent(JSON.parse(data.lesson.content))
        setLessonExercises(JSON.parse(data.lesson.exercises))
        setStartTime(new Date())
      }
    } catch (error) {
      console.error("Error fetching lesson:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAnswerChange = (questionId: string, answer: string) => {
    setUserAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }))
  }

  const calculateScore = () => {
    if (!lessonExercises) return 0
    
    let correctAnswers = 0
    let totalQuestions = 0

    // Check matching exercises
    if (lessonExercises.matching) {
      lessonExercises.matching.forEach((exercise, index) => {
        totalQuestions++
        if (userAnswers[`matching_${index}`] === exercise.correctAnswer) {
          correctAnswers++
        }
      })
    }

    // Check fill-in-blanks exercises
    if (lessonExercises.fillInBlanks) {
      lessonExercises.fillInBlanks.forEach((exercise, index) => {
        totalQuestions++
        if (userAnswers[`fillInBlanks_${index}`]?.toLowerCase().trim() === exercise.answer.toLowerCase().trim()) {
          correctAnswers++
        }
      })
    }

    return Math.round((correctAnswers / totalQuestions) * 100)
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    
    try {
      const calculatedScore = calculateScore()
      setScore(calculatedScore)
      
      const timeSpent = startTime ? Math.round((new Date().getTime() - startTime.getTime()) / 1000 / 60) : 0
      
      const response = await fetch(`/api/lessons/${lessonId}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          answers: userAnswers,
          score: calculatedScore,
          timeSpent
        })
      })

      if (response.ok) {
        setShowResults(true)
      }
    } catch (error) {
      console.error("Error submitting lesson:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreMessage = (score: number) => {
    if (score >= 90) return "Excellent! すばらしい！"
    if (score >= 70) return "Good job! よくできました！"
    if (score >= 60) return "Keep practicing! 練習を続けましょう！"
    return "Try again! もう一度挑戦しましょう！"
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p>Loading lesson...</p>
        </div>
      </div>
    )
  }

  if (!session || !lesson) {
    return null
  }

  if (showResults) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl">Lesson Complete!</CardTitle>
              <CardDescription>Great job completing "{lesson.title}"</CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="space-y-2">
                <div className={`text-4xl font-bold ${getScoreColor(score)}`}>
                  {score}%
                </div>
                <p className="text-lg text-gray-600">
                  {getScoreMessage(score)}
                </p>
              </div>
              
              <div className="flex justify-center space-x-4">
                <Link href={`/lesson/${lessonId}?id=${lessonId}`}>
                  <Button variant="outline">
                    Review Lesson
                  </Button>
                </Link>
                <Link href={`/dashboard`}>
                  <Button>
                    Back to Dashboard
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href={`/dashboard`}>
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{lesson.title}</h1>
                <p className="text-sm text-gray-600">
                  {lesson.module.title} • {lesson.module.roadmap.title}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{lesson.module.type}</Badge>
              {lesson.progress?.[0]?.status === "COMPLETED" && (
                <Badge variant="default">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Completed
                </Badge>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="content">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Content
                </TabsTrigger>
                <TabsTrigger value="exercises">
                  <Trophy className="w-4 h-4 mr-2" />
                  Exercises
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="content" className="space-y-6">
                {lessonContent?.explanations.map((explanation, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle>{explanation.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-gray-700">{explanation.content}</p>
                      {explanation.examples && (
                        <div className="space-y-2">
                          <h4 className="font-semibold">Examples:</h4>
                          {explanation.examples.map((example, exampleIndex) => (
                            <div key={exampleIndex} className="bg-gray-50 p-3 rounded-lg">
                              <div className="font-medium text-lg">{example.japanese}</div>
                              <div className="text-gray-600">{example.reading}</div>
                              <div className="text-gray-700">{example.meaning}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
                
                {lessonContent?.culturalNotes && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Cultural Notes</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {lessonContent.culturalNotes.map((note, index) => (
                        <div key={index}>
                          <h4 className="font-semibold">{note.title}</h4>
                          <p className="text-gray-700">{note.content}</p>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
              
              <TabsContent value="exercises" className="space-y-6">
                {lessonExercises?.matching && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Matching Exercises</CardTitle>
                      <CardDescription>Match the Japanese with the correct meaning</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {lessonExercises.matching.map((exercise, index) => (
                        <div key={index} className="space-y-2">
                          <p className="font-medium">{exercise.question}</p>
                          <RadioGroup
                            value={userAnswers[`matching_${index}`] || ""}
                            onValueChange={(value) => handleAnswerChange(`matching_${index}`, value)}
                          >
                            {exercise.options.map((option, optionIndex) => (
                              <div key={optionIndex} className="flex items-center space-x-2">
                                <RadioGroupItem value={option} id={`matching_${index}_${optionIndex}`} />
                                <Label htmlFor={`matching_${index}_${optionIndex}`}>{option}</Label>
                              </div>
                            ))}
                          </RadioGroup>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}
                
                {lessonExercises?.fillInBlanks && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Fill in the Blanks</CardTitle>
                      <CardDescription>Complete the sentences with the correct words</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {lessonExercises.fillInBlanks.map((exercise, index) => (
                        <div key={index} className="space-y-2">
                          <p className="font-medium">{exercise.sentence}</p>
                          {exercise.options ? (
                            <RadioGroup
                              value={userAnswers[`fillInBlanks_${index}`] || ""}
                              onValueChange={(value) => handleAnswerChange(`fillInBlanks_${index}`, value)}
                            >
                              {exercise.options.map((option, optionIndex) => (
                                <div key={optionIndex} className="flex items-center space-x-2">
                                  <RadioGroupItem value={option} id={`fillInBlanks_${index}_${optionIndex}`} />
                                  <Label htmlFor={`fillInBlanks_${index}_${optionIndex}`}>{option}</Label>
                                </div>
                              ))}
                            </RadioGroup>
                          ) : (
                            <Input
                              placeholder="Type your answer..."
                              value={userAnswers[`fillInBlanks_${index}`] || ""}
                              onChange={(e) => handleAnswerChange(`fillInBlanks_${index}`, e.target.value)}
                            />
                          )}
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}
                
                {lessonExercises?.vocabulary && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Vocabulary</CardTitle>
                      <CardDescription>Study these important words and phrases</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-3">
                        {lessonExercises.vocabulary.map((item, index) => (
                          <div key={index} className="bg-gray-50 p-3 rounded-lg">
                            <div className="font-medium text-lg">{item.word}</div>
                            <div className="text-gray-600">{item.reading}</div>
                            <div className="text-gray-700">{item.meaning}</div>
                            <Badge variant="outline" className="mt-1">{item.type}</Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
                
                {lessonExercises?.grammar && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Grammar Points</CardTitle>
                      <CardDescription>Key grammar patterns and usage</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {lessonExercises.grammar.map((item, index) => (
                        <div key={index} className="space-y-2">
                          <h4 className="font-semibold">{item.pattern}</h4>
                          <p className="text-gray-700">{item.meaning}</p>
                          {item.examples && (
                            <div className="space-y-1">
                              {item.examples.map((example, exampleIndex) => (
                                <div key={exampleIndex} className="text-sm bg-gray-50 p-2 rounded">
                                  <div>{example.japanese}</div>
                                  <div className="text-gray-600">{example.reading}</div>
                                  <div className="text-gray-700">{example.meaning}</div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}
                
                {lessonExercises?.kanji && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Kanji</CardTitle>
                      <CardDescription>Learn these kanji characters</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4">
                        {lessonExercises.kanji.map((item, index) => (
                          <div key={index} className="bg-gray-50 p-4 rounded-lg">
                            <div className="text-center">
                              <div className="text-4xl font-bold mb-2">{item.character}</div>
                              <div className="text-lg font-semibold">{item.meaning}</div>
                              <div className="text-gray-600">
                                Strokes: {item.strokeCount}
                              </div>
                              <div className="mt-2 space-y-1">
                                {item.readings.map((reading, readingIndex) => (
                                  <div key={readingIndex} className="text-sm">
                                    <span className="font-medium">{reading.type}:</span> {reading.reading}
                                  </div>
                                ))}
                              </div>
                              {item.examples && (
                                <div className="mt-3 space-y-1">
                                  <div className="font-medium text-sm">Examples:</div>
                                  {item.examples.map((example, exampleIndex) => (
                                    <div key={exampleIndex} className="text-xs">
                                      {example.word} • {example.reading} • {example.meaning}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>
          
          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Completion</span>
                  <span className="text-sm font-medium">
                    {lesson.progress?.[0]?.progress || 0}%
                  </span>
                </div>
                <Progress value={lesson.progress?.[0]?.progress || 0} className="h-2" />
                
                {startTime && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="w-4 h-4 mr-1" />
                    Time spent: {Math.round((new Date().getTime() - startTime.getTime()) / 1000 / 60)} min
                  </div>
                )}
                
                {currentTab === "exercises" && (
                  <Button 
                    onClick={handleSubmit} 
                    disabled={isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting ? "Submitting..." : "Submit Answers"}
                  </Button>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Lesson Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="text-sm text-gray-600">Module:</span>
                  <div className="font-medium">{lesson.module.title}</div>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Type:</span>
                  <div className="font-medium">{lesson.module.type}</div>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Order:</span>
                  <div className="font-medium">Lesson {lesson.order}</div>
                </div>
              </CardContent>
            </Card>
            
            {lesson.history?.[0] && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Previous Attempt</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Score:</span>
                    <span className="font-medium">{lesson.history[0].score}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Time:</span>
                    <span className="font-medium">{lesson.history[0].timeSpent} min</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Completed:</span>
                    <span className="font-medium">
                      {new Date(lesson.history[0].completedAt).toLocaleDateString()}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}