"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, BookOpen, Clock, CheckCircle, XCircle, RotateCcw, Trophy } from "lucide-react"
import Link from "next/link"

interface LessonContent {
  explanations: Array<{
    title: string
    content: string
    examples?: Array<{
      japanese: string
      reading: string
      meaning: string
    }>
  }>
  culturalNotes?: Array<{
    title: string
    content: string
  }>
}

interface LessonExercises {
  vocabulary?: Array<{
    word: string
    reading: string
    meaning: string
    type: string
  }>
  grammar?: Array<{
    pattern: string
    meaning: string
    examples: Array<{
      japanese: string
      reading: string
      meaning: string
    }>
    practice: Array<{
      question: string
      answer: string
    }>
  }>
  kanji?: Array<{
    character: string
    meaning: string
    readings: Array<{
      type: string
      reading: string
    }>
    strokeCount: number
    examples: Array<{
      word: string
      reading: string
      meaning: string
    }>
  }>
  matching?: Array<{
    question: string
    options: string[]
    correctAnswer: string
  }>
  fillInBlanks?: Array<{
    sentence: string
    answer: string
    options?: string[]
  }>
}

interface Lesson {
  id: string
  title: string
  description: string
  content: string
  exercises: string
  order: number
  module: {
    id: string
    title: string
    type: string
    roadmap: {
      id: string
      title: string
    }
  }
  progress?: Array<{
    status: string
    progress: number
  }>
  history?: Array<{
    score: number
    timeSpent: number
    completedAt: string
  }>
}

interface UserAnswer {
  exerciseId: string
  exerciseType: string
  question: string
  userAnswer: string
  correctAnswer: string
  isCorrect: boolean
}

export default function LessonPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [lesson, setLesson] = useState<Lesson | null>(null)
  const [lessonContent, setLessonContent] = useState<LessonContent | null>(null)
  const [lessonExercises, setLessonExercises] = useState<LessonExercises | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([])
  const [currentTab, setCurrentTab] = useState("content")
  const [startTime, setStartTime] = useState<Date | null>(null)
  const [showResults, setShowResults] = useState(false)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (params.id && session) {
      fetchLesson()
    }
  }, [params.id, session, status, router])

  const fetchLesson = async () => {
    try {
      const response = await fetch(`/api/lessons/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setLesson(data.lesson)
        
        // Parse content and exercises
        if (data.lesson.content) {
          setLessonContent(JSON.parse(data.lesson.content))
        }
        if (data.lesson.exercises) {
          setLessonExercises(JSON.parse(data.lesson.exercises))
        }
        
        setStartTime(new Date())
      } else {
        router.push("/dashboard")
      }
    } catch (error) {
      console.error("Error fetching lesson:", error)
      router.push("/dashboard")
    } finally {
      setIsLoading(false)
    }
  }

  const handleAnswerChange = (exerciseId: string, exerciseType: string, question: string, correctAnswer: string, userAnswer: string) => {
    setUserAnswers(prev => {
      const existing = prev.find(a => a.exerciseId === exerciseId)
      if (existing) {
        return prev.map(a => 
          a.exerciseId === exerciseId 
            ? { ...a, userAnswer, isCorrect: userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim() }
            : a
        )
      } else {
        return [...prev, {
          exerciseId,
          exerciseType,
          question,
          userAnswer,
          correctAnswer,
          isCorrect: userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim()
        }]
      }
    })
  }

  const calculateScore = () => {
    if (userAnswers.length === 0) return 0
    const correct = userAnswers.filter(a => a.isCorrect).length
    return Math.round((correct / userAnswers.length) * 100)
  }

  const handleSubmitLesson = async () => {
    if (!startTime || !lesson) return

    setIsSubmitting(true)
    const timeSpent = Math.round((new Date().getTime() - startTime.getTime()) / 1000 / 60) // minutes
    const score = calculateScore()

    try {
      const response = await fetch(`/api/lessons/${lesson.id}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          answers: userAnswers,
          score,
          timeSpent
        })
      })

      if (response.ok) {
        setShowResults(true)
      }
    } catch (error) {
      console.error("Error submitting lesson:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetLesson = () => {
    setUserAnswers([])
    setShowResults(false)
    setStartTime(new Date())
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreMessage = (score: number) => {
    if (score >= 90) return "Excellent! 🎉"
    if (score >= 70) return "Good job! 👍"
    if (score >= 50) return "Keep practicing! 💪"
    return "Try again! 📚"
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p>Loading lesson...</p>
        </div>
      </div>
    )
  }

  if (!lesson || !lessonContent || !lessonExercises) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p>Lesson not found</p>
          <Link href="/dashboard">
            <Button className="mt-4">Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  const score = calculateScore()
  const totalExercises = [
    ...(lessonExercises.vocabulary?.length || 0),
    ...(lessonExercises.grammar?.length || 0),
    ...(lessonExercises.kanji?.length || 0),
    ...(lessonExercises.matching?.length || 0),
    ...(lessonExercises.fillInBlanks?.length || 0)
  ].reduce((a, b) => a + b, 0)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <Link href={`/dashboard?roadmap=${lesson.module.roadmap.id}`}>
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{lesson.title}</h1>
                <p className="text-sm text-gray-600">
                  {lesson.module.title} • {lesson.module.roadmap.title}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{lesson.module.type}</Badge>
              {lesson.progress?.[0]?.status === "COMPLETED" && (
                <Badge variant="default">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Completed
                </Badge>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {showResults ? (
          <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl">Lesson Completed!</CardTitle>
              <CardDescription>
                {getScoreMessage(score)}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className={`text-4xl font-bold ${getScoreColor(score)}`}>
                  {score}%
                </div>
                <p className="text-gray-600 mt-2">
                  {userAnswers.filter(a => a.isCorrect).length} of {userAnswers.length} correct
                </p>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold">Review your answers:</h3>
                {userAnswers.map((answer, index) => (
                  <div key={answer.exerciseId} className="border rounded-lg p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{answer.question}</p>
                        <p className="text-sm text-gray-600 mt-1">
                          Your answer: <span className={answer.isCorrect ? "text-green-600" : "text-red-600"}>
                            {answer.userAnswer || "(empty)"}
                          </span>
                        </p>
                        {!answer.isCorrect && (
                          <p className="text-sm text-green-600">
                            Correct answer: {answer.correctAnswer}
                          </p>
                        )}
                      </div>
                      {answer.isCorrect ? (
                        <CheckCircle className="w-5 h-5 text-green-600 mt-1" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-600 mt-1" />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex space-x-3">
                <Link href={`/dashboard?roadmap=${lesson.module.roadmap.id}`}>
                  <Button className="flex-1">Back to Dashboard</Button>
                </Link>
                <Button variant="outline" onClick={resetLesson} className="flex-1">
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Retry Lesson
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="exercises">Exercises ({totalExercises})</TabsTrigger>
                </TabsList>
                
                <TabsContent value="content" className="space-y-6">
                  {/* Explanations */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Lesson Content</CardTitle>
                      <CardDescription>
                        Study the material below, then try the exercises
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {lessonContent.explanations.map((explanation, index) => (
                        <div key={index} className="space-y-3">
                          <h3 className="text-lg font-semibold">{explanation.title}</h3>
                          <p className="text-gray-700 whitespace-pre-wrap">{explanation.content}</p>
                          {explanation.examples && explanation.examples.length > 0 && (
                            <div className="bg-gray-50 rounded-lg p-4">
                              <h4 className="font-medium text-sm text-gray-900 mb-2">Examples:</h4>
                              <div className="space-y-2">
                                {explanation.examples.map((example, exampleIndex) => (
                                  <div key={exampleIndex} className="border-l-2 border-red-200 pl-3">
                                    <p className="font-medium text-japanese">{example.japanese}</p>
                                    <p className="text-sm text-gray-600">{example.reading}</p>
                                    <p className="text-sm text-gray-700">{example.meaning}</p>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  {/* Cultural Notes */}
                  {lessonContent.culturalNotes && lessonContent.culturalNotes.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Cultural Notes</CardTitle>
                        <CardDescription>
                          Learn about Japanese culture and context
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {lessonContent.culturalNotes.map((note, index) => (
                          <div key={index} className="space-y-2">
                            <h3 className="font-semibold">{note.title}</h3>
                            <p className="text-gray-700 whitespace-pre-wrap">{note.content}</p>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="exercises" className="space-y-6">
                  {/* Vocabulary Exercises */}
                  {lessonExercises.vocabulary && lessonExercises.vocabulary.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Vocabulary Practice</CardTitle>
                        <CardDescription>
                          Match the Japanese words with their meanings
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {lessonExercises.vocabulary.map((item, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid md:grid-cols-2 gap-4">
                              <div>
                                <p className="font-medium text-japanese text-lg">{item.word}</p>
                                <p className="text-sm text-gray-600">{item.reading}</p>
                              </div>
                              <div>
                                <Input
                                  placeholder="Enter the meaning"
                                  onChange={(e) => handleAnswerChange(
                                    `vocab-${index}`,
                                    "vocabulary",
                                    item.word,
                                    item.meaning,
                                    e.target.value
                                  )}
                                  value={userAnswers.find(a => a.exerciseId === `vocab-${index}`)?.userAnswer || ""}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  )}

                  {/* Matching Exercises */}
                  {lessonExercises.matching && lessonExercises.matching.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Matching Exercise</CardTitle>
                        <CardDescription>
                          Match each question with the correct answer
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {lessonExercises.matching.map((item, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <p className="font-medium mb-3">{item.question}</p>
                            <Select
                              onValueChange={(value) => handleAnswerChange(
                                `matching-${index}`,
                                "matching",
                                item.question,
                                item.correctAnswer,
                                value
                              )}
                              value={userAnswers.find(a => a.exerciseId === `matching-${index}`)?.userAnswer || ""}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select your answer" />
                              </SelectTrigger>
                              <SelectContent>
                                {item.options.map((option, optionIndex) => (
                                  <SelectItem key={optionIndex} value={option}>
                                    {option}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  )}

                  {/* Fill in the Blanks */}
                  {lessonExercises.fillInBlanks && lessonExercises.fillInBlanks.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Fill in the Blanks</CardTitle>
                        <CardDescription>
                          Complete the sentences with the correct words
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {lessonExercises.fillInBlanks.map((item, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <p className="mb-3">{item.sentence}</p>
                            {item.options ? (
                              <Select
                                onValueChange={(value) => handleAnswerChange(
                                  `fill-${index}`,
                                  "fillInBlanks",
                                  item.sentence,
                                  item.answer,
                                  value
                                )}
                                value={userAnswers.find(a => a.exerciseId === `fill-${index}`)?.userAnswer || ""}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select the correct word" />
                                </SelectTrigger>
                                <SelectContent>
                                  {item.options.map((option, optionIndex) => (
                                    <SelectItem key={optionIndex} value={option}>
                                      {option}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            ) : (
                              <Input
                                placeholder="Enter the missing word"
                                onChange={(e) => handleAnswerChange(
                                  `fill-${index}`,
                                  "fillInBlanks",
                                  item.sentence,
                                  item.answer,
                                  e.target.value
                                )}
                                value={userAnswers.find(a => a.exerciseId === `fill-${index}`)?.userAnswer || ""}
                              />
                            )}
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  )}

                  <div className="flex justify-between items-center pt-4 border-t">
                    <div className="text-sm text-gray-600">
                      {userAnswers.length} of {totalExercises} exercises completed
                    </div>
                    <Button 
                      onClick={handleSubmitLesson} 
                      disabled={isSubmitting || userAnswers.length === 0}
                      className="px-8"
                    >
                      {isSubmitting ? "Submitting..." : "Submit Lesson"}
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Progress Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Progress</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Exercises</span>
                      <span>{userAnswers.length}/{totalExercises}</span>
                    </div>
                    <Progress 
                      value={totalExercises > 0 ? (userAnswers.length / totalExercises) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                  
                  {startTime && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="w-4 h-4 mr-2" />
                      {Math.round((new Date().getTime() - startTime.getTime()) / 1000 / 60)} min
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Quick Navigation */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Navigation</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start"
                    onClick={() => setCurrentTab("content")}
                  >
                    <BookOpen className="w-4 h-4 mr-2" />
                    Study Content
                  </Button>
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start"
                    onClick={() => setCurrentTab("exercises")}
                  >
                    Practice Exercises
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}