import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import ZAI from 'z-ai-web-dev-sdk'
import { UserLevel, ModuleType } from "@prisma/client"

interface RoadmapModule {
  title: string
  description: string
  type: ModuleType
  estimatedDays: number
  order: number
  milestones: Array<{
    title: string
    description: string
    criteria: string
    order: number
  }>
}

interface GeneratedRoadmap {
  title: string
  description: string
  estimatedWeeks: number
  modules: RoadmapModule[]
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { targetLevel, learningGoals, timeCommitment } = await request.json()

    if (!targetLevel || !learningGoals || !timeCommitment) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Get user info
    const user = await db.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Initialize ZAI SDK
    const zai = await ZAI.create()

    // Generate roadmap using AI
    const prompt = `
    You are an expert Japanese language instructor. Create a personalized learning roadmap for a student.

    Student Information:
    - Current Level: ${user.level}
    - Target Level: ${targetLevel}
    - Learning Goals: ${learningGoals}
    - Daily Time Commitment: ${timeCommitment} minutes
    - Current Goals: ${user.learningGoals}

    Generate a comprehensive roadmap that follows pedagogical principles:
    1. Start with foundations (hiragana/katakana for beginners)
    2. Progress logically through grammar patterns
    3. Include vocabulary building
    4. Add kanji learning progressively
    5. Include review sessions every 3-5 modules
    6. Adapt complexity based on goals (conversation vs. writing focus)

    Return the roadmap in this JSON format:
    {
      "title": "Roadmap title",
      "description": "Brief description of the roadmap",
      "estimatedWeeks": number,
      "modules": [
        {
          "title": "Module title",
          "description": "Module description",
          "type": "HIRAGANA|KATAKANA|VOCABULARY|GRAMMAR|KANJI|CONVERSATION|LISTENING|READING|WRITING|REVIEW",
          "estimatedDays": number,
          "order": number,
          "milestones": [
            {
              "title": "Milestone title",
              "description": "Milestone description",
              "criteria": "Completion criteria",
              "order": number
            }
          ]
        }
      ]
    }

    Ensure the roadmap is realistic and achievable within the time constraints.
    Include a mix of learning types appropriate for the student's goals.
    `

    const completion = await zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert Japanese language instructor specializing in creating personalized learning roadmaps.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })

    let roadmapData: GeneratedRoadmap
    try {
      const content = completion.choices[0]?.message?.content
      if (!content) {
        throw new Error("No content received from AI")
      }
      
      // Extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error("No JSON found in AI response")
      }
      
      roadmapData = JSON.parse(jsonMatch[0])
    } catch (error) {
      console.error("Error parsing AI response:", error)
      return NextResponse.json({ error: "Failed to generate roadmap" }, { status: 500 })
    }

    // Create roadmap in database
    const roadmap = await db.roadmap.create({
      data: {
        title: roadmapData.title,
        description: roadmapData.description,
        targetLevel: targetLevel as UserLevel,
        timeCommitment: parseInt(timeCommitment),
        estimatedWeeks: roadmapData.estimatedWeeks,
        userId: session.user.id,
        modules: {
          create: roadmapData.modules.map((module: RoadmapModule) => ({
            title: module.title,
            description: module.description,
            type: module.type,
            estimatedDays: module.estimatedDays,
            order: module.order,
            milestones: {
              create: module.milestones.map((milestone) => ({
                title: milestone.title,
                description: milestone.description,
                criteria: milestone.criteria,
                order: milestone.order
              }))
            }
          }))
        }
      },
      include: {
        modules: {
          include: {
            milestones: true
          }
        }
      }
    })

    return NextResponse.json({ roadmap })
  } catch (error) {
    console.error("Roadmap generation error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}