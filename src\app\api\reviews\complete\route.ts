import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { SpacedRepetitionScheduler } from "@/lib/spaced-repetition"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { sessionId, results, timeSpent } = await request.json()

    if (!sessionId || !results || !timeSpent) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Get the review session
    const reviewSession = await db.reviewSession.findUnique({
      where: { id: sessionId },
      include: {
        reviewItems: {
          include: {
            lesson: true
          }
        },
        user: true
      }
    })

    if (!reviewSession) {
      return NextResponse.json({ error: "Review session not found" }, { status: 404 })
    }

    if (reviewSession.userId !== session.user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Calculate average score
    const totalScore = results.reduce((sum: number, result: any) => sum + result.score, 0)
    const averageScore = totalScore / results.length

    // Update review session
    await db.reviewSession.update({
      where: { id: sessionId },
      data: {
        score: averageScore,
        timeSpent: timeSpent
      }
    })

    // Update each review item based on performance
    for (const result of results) {
      const reviewItem = reviewSession.reviewItems.find(item => item.id === result.reviewItemId)
      if (!reviewItem) continue

      const quality = SpacedRepetitionScheduler.scoreToQuality(result.score)
      const { interval, easeFactor } = SpacedRepetitionScheduler.calculateNextReview(
        reviewItem.interval,
        reviewItem.easeFactor,
        quality
      )

      // Update difficulty based on performance
      const newDifficulty = Math.max(0, Math.min(1, 
        reviewItem.difficulty + (quality < 3 ? 0.1 : -0.05)
      ))

      // Calculate next review date
      const nextReview = new Date()
      nextReview.setDate(nextReview.getDate() + interval)

      // Update the review item
      await db.reviewItem.update({
        where: { id: result.reviewItemId },
        data: {
          difficulty: newDifficulty,
          interval: interval,
          easeFactor: easeFactor,
          nextReview: nextReview
        }
      })
    }

    // Get updated review statistics
    const allReviewItems = await db.reviewItem.findMany({
      where: {
        lesson: {
          module: {
            roadmap: {
              userId: session.user.id
            }
          }
        }
      }
    })

    const stats = SpacedRepetitionScheduler.generateReviewStats(
      allReviewItems.map(item => ({
        id: item.lessonId,
        type: item.itemType,
        difficulty: item.difficulty,
        interval: item.interval,
        easeFactor: item.easeFactor,
        nextReview: item.nextReview,
        consecutiveCorrect: 0, // We don't track this in the current schema
        consecutiveIncorrect: 0
      }))
    )

    return NextResponse.json({ 
      success: true,
      averageScore,
      stats,
      message: averageScore >= 80 ? "Excellent work!" : 
               averageScore >= 60 ? "Good job! Keep practicing." : 
               "Keep practicing! Review challenging items again soon."
    })

  } catch (error) {
    console.error("Review completion error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}