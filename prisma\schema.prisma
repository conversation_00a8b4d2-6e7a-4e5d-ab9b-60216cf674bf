// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  name          String?
  level         UserLevel @default(BEGINNER)
  learningGoals String   // JSON array of learning goals
  timeCommitment Int     // Minutes per day
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relations
  roadmaps      Roadmap[]
  progress      UserProgress[]
  reviews       ReviewSession[]
  lessonHistory LessonHistory[]
  
  @@map("users")
}

model Roadmap {
  id             String   @id @default(cuid())
  title          String
  description    String
  targetLevel    UserLevel
  timeCommitment Int      // Minutes per day
  estimatedWeeks Int
  userId         String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  // Relations
  user           User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  modules        Module[]
  progress       UserProgress[]
  
  @@map("roadmaps")
}

model Module {
  id          String   @id @default(cuid())
  title       String
  description String
  order       Int
  type        ModuleType
  estimatedDays Int
  roadmapId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  roadmap     Roadmap    @relation(fields: [roadmapId], references: [id], onDelete: Cascade)
  lessons     Lesson[]
  milestones  Milestone[]
  progress    UserProgress[]
  
  @@map("modules")
}

model Lesson {
  id          String   @id @default(cuid())
  title       String
  description String
  content     String   // JSON content including explanations, examples, cultural notes
  exercises   String   // JSON exercises (vocabulary, grammar, kanji)
  order       Int
  moduleId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  module      Module         @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  progress    UserProgress[]
  history     LessonHistory[]
  reviews     ReviewItem[]
  
  @@map("lessons")
}

model Milestone {
  id          String   @id @default(cuid())
  title       String
  description String
  criteria    String   // JSON criteria for completion
  order       Int
  moduleId    String
  achieved    Boolean  @default(false)
  achievedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  module      Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  
  @@map("milestones")
}

model UserProgress {
  id           String   @id @default(cuid())
  userId       String
  roadmapId    String
  moduleId     String?
  lessonId     String?
  status       ProgressStatus @default(NOT_STARTED)
  progress     Float    @default(0) // 0-100 percentage
  startedAt    DateTime?
  completedAt  DateTime?
  timeSpent    Int      @default(0) // Minutes spent
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Relations
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  roadmap      Roadmap  @relation(fields: [roadmapId], references: [id], onDelete: Cascade)
  module       Module?  @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  lesson       Lesson?  @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  
  @@unique([userId, roadmapId, moduleId, lessonId])
  @@map("user_progress")
}

model LessonHistory {
  id          String   @id @default(cuid())
  userId      String
  lessonId    String
  score       Float    // 0-100
  timeSpent   Int      // Minutes
  answers     String   // JSON array of user answers
  completedAt DateTime @default(now())
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  lesson      Lesson   @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  
  @@map("lesson_history")
}

model ReviewSession {
  id          String   @id @default(cuid())
  userId      String
  type        ReviewType
  items       Int      @default(0) // Number of items reviewed
  score       Float    // Average score
  timeSpent   Int      // Minutes
  completedAt DateTime @default(now())
  
  // Relations
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  reviewItems ReviewItem[]
  
  @@map("review_sessions")
}

model ReviewItem {
  id           String   @id @default(cuid())
  sessionId    String
  lessonId     String
  itemType     ReviewItemType
  itemId       String   // ID of the specific item (vocabulary, grammar point, etc.)
  difficulty   Float    // 0-1, adjusted based on performance
  nextReview   DateTime // When to review next
  interval     Int      // Days until next review
  easeFactor   Float    @default(2.5) // Spaced repetition ease factor
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Relations
  session      ReviewSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  lesson       Lesson        @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  
  @@map("review_items")
}

// Knowledge base models
model Vocabulary {
  id          String   @id @default(cuid())
  word        String
  reading     String   // Hiragana/Katakana reading
  meaning     String
  level       UserLevel
  category    String   // e.g., "noun", "verb", "adjective"
  example     String   // Example sentence
  jlptLevel   String?  // N5, N4, N3, N2, N1
  frequency   Int?     // Usage frequency
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("vocabulary")
}

model Grammar {
  id          String   @id @default(cuid())
  pattern     String   // e.g., "〜です/ます"
  meaning     String
  explanation String
  level       UserLevel
  category    String   // e.g., "polite form", "te-form", "conditional"
  examples    String   // JSON array of example sentences
  jlptLevel   String?  // N5, N4, N3, N2, N1
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("grammar")
}

model Kanji {
  id          String   @id @default(cuid())
  character   String
  meaning     String
  readings    String   // JSON array of readings (kun'yomi, on'yomi)
  strokeCount Int
  level       UserLevel
  jlptLevel   String?  // N5, N4, N3, N2, N1
  grade       Int?     // School grade level
  examples    String   // JSON array of example words
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("kanji")
}

// Enums
enum UserLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum ModuleType {
  HIRAGANA
  KATAKANA
  VOCABULARY
  GRAMMAR
  KANJI
  CONVERSATION
  LISTENING
  READING
  WRITING
  REVIEW
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  PAUSED
}

enum ReviewType {
  VOCABULARY
  GRAMMAR
  KANJI
  MIXED
}

enum ReviewItemType {
  VOCABULARY
  GRAMMAR_POINT
  KANJI_CHARACTER
  SENTENCE
}