"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Clock, Target, CheckCircle, XCircle, RotateCcw, Trophy, Brain } from "lucide-react"
import Link from "next/link"

interface ReviewSession {
  id: string
  type: string
  items: number
  score: number
  timeSpent: number
  completedAt: string
}

interface ReviewItem {
  id: string
  lessonId: string
  itemType: string
  difficulty: number
  nextReview: string
  lesson: {
    id: string
    title: string
    content: string
    exercises: string
    module: {
      title: string
      type: string
    }
  }
}

interface ReviewStats {
  totalItems: number
  dueItems: number
  upcomingItems: number
  averageDifficulty: number
  averageInterval: number
}

export default function ReviewPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [reviewSession, setReviewSession] = useState<ReviewSession | null>(null)
  const [reviewItems, setReviewItems] = useState<ReviewItem[]>([])
  const [reviewStats, setReviewStats] = useState<ReviewStats | null>(null)
  const [currentItemIndex, setCurrentItemIndex] = useState(0)
  const [userAnswers, setUserAnswers] = useState<Array<{reviewItemId: string; answer: string; score: number}>>([])
  const [showResults, setShowResults] = useState(false)
  const [startTime, setStartTime] = useState<Date | null>(null)
  const [reviewType, setReviewType] = useState("MIXED")

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (session) {
      fetchReviewStats()
    }
  }, [session, status, router])

  const fetchReviewStats = async () => {
    try {
      // Generate a review session to get stats
      const response = await fetch("/api/reviews/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ reviewType, maxItems: 0 })
      })

      if (response.ok) {
        const data = await response.json()
        setReviewStats(data.stats)
      }
    } catch (error) {
      console.error("Error fetching review stats:", error)
    }
  }

  const generateReviewSession = async () => {
    setIsGenerating(true)
    
    try {
      const response = await fetch("/api/reviews/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ reviewType })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.reviewItems.length > 0) {
          setReviewSession(data.reviewSession)
          setReviewItems(data.reviewItems)
          setReviewStats(data.stats)
          setCurrentItemIndex(0)
          setUserAnswers([])
          setShowResults(false)
          setStartTime(new Date())
        } else {
          alert("No items due for review at this time.")
        }
      }
    } catch (error) {
      console.error("Error generating review session:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAnswerSubmit = (reviewItemId: string, answer: string, score: number) => {
    setUserAnswers(prev => {
      const existing = prev.find(a => a.reviewItemId === reviewItemId)
      if (existing) {
        return prev.map(a => 
          a.reviewItemId === reviewItemId 
            ? { ...a, answer, score }
            : a
        )
      } else {
        return [...prev, { reviewItemId, answer, score }]
      }
    })

    // Move to next item or show results
    if (currentItemIndex < reviewItems.length - 1) {
      setCurrentItemIndex(prev => prev + 1)
    } else {
      completeReviewSession()
    }
  }

  const completeReviewSession = async () => {
    if (!reviewSession || !startTime) return

    setIsSubmitting(true)
    const timeSpent = Math.round((new Date().getTime() - startTime.getTime()) / 1000 / 60) // minutes

    try {
      const response = await fetch("/api/reviews/complete", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sessionId: reviewSession.id,
          results: userAnswers,
          timeSpent
        })
      })

      if (response.ok) {
        const data = await response.json()
        setReviewStats(data.stats)
        setShowResults(true)
      }
    } catch (error) {
      console.error("Error completing review session:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetReview = () => {
    setReviewSession(null)
    setReviewItems([])
    setCurrentItemIndex(0)
    setUserAnswers([])
    setShowResults(false)
    setStartTime(null)
  }

  const getCurrentItem = () => {
    return reviewItems[currentItemIndex]
  }

  const getProgress = () => {
    return ((currentItemIndex + 1) / reviewItems.length) * 100
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getItemTypeColor = (itemType: string) => {
    const colors = {
      VOCABULARY: "bg-purple-100 text-purple-800",
      GRAMMAR_POINT: "bg-orange-100 text-orange-800",
      KANJI_CHARACTER: "bg-red-100 text-red-800",
      SENTENCE: "bg-blue-100 text-blue-800"
    }
    return colors[itemType as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Spaced Repetition Review</h1>
                <p className="text-sm text-gray-600">Review and reinforce your learning</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-600" />
              <Badge variant="outline">AI-Powered Review</Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!reviewSession ? (
          <div className="max-w-4xl mx-auto">
            {/* Stats Overview */}
            {reviewStats && (
              <div className="grid md:grid-cols-4 gap-4 mb-8">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{reviewStats.totalItems}</div>
                    <div className="text-sm text-gray-600">Total Items</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">{reviewStats.dueItems}</div>
                    <div className="text-sm text-gray-600">Due for Review</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-yellow-600">{reviewStats.upcomingItems}</div>
                    <div className="text-sm text-gray-600">Upcoming</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round(reviewStats.averageDifficulty * 100)}%
                    </div>
                    <div className="text-sm text-gray-600">Avg Difficulty</div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Start Review Card */}
            <Card>
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="w-8 h-8 text-purple-600" />
                </div>
                <CardTitle className="text-2xl">Start Review Session</CardTitle>
                <CardDescription>
                  Practice items that are due for review using spaced repetition
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Review Type</label>
                  <Select value={reviewType} onValueChange={setReviewType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MIXED">Mixed Review</SelectItem>
                      <SelectItem value="VOCABULARY">Vocabulary</SelectItem>
                      <SelectItem value="GRAMMAR">Grammar</SelectItem>
                      <SelectItem value="KANJI">Kanji</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium mb-2">How it works:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Items are scheduled based on your performance</li>
                    <li>• Correct answers increase the review interval</li>
                    <li>• Incorrect answers reset the interval</li>
                    <li>• Focus on items you find challenging</li>
                  </ul>
                </div>

                <Button 
                  onClick={generateReviewSession} 
                  disabled={isGenerating || (reviewStats?.dueItems === 0)}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? "Generating Session..." : 
                   reviewStats?.dueItems === 0 ? "No Items Due" : "Start Review Session"}
                </Button>
              </CardContent>
            </Card>
          </div>
        ) : showResults ? (
          <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl">Review Completed!</CardTitle>
              <CardDescription>
                Great job completing your review session
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600">
                  {Math.round(userAnswers.reduce((sum, a) => sum + a.score, 0) / userAnswers.length * 100)}%
                </div>
                <p className="text-gray-600 mt-2">Average Score</p>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold">Session Summary:</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-gray-50 p-3 rounded">
                    <div className="font-medium">Items Reviewed</div>
                    <div className="text-lg">{userAnswers.length}</div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <div className="font-medium">Time Spent</div>
                    <div className="text-lg">{Math.round((new Date().getTime() - (startTime?.getTime() || 0)) / 1000 / 60)} min</div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <Button onClick={resetReview} className="flex-1">
                  Back to Review Home
                </Button>
                <Button variant="outline" onClick={generateReviewSession} className="flex-1">
                  <RotateCcw className="w-4 h-4 mr-2" />
                  New Session
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="max-w-4xl mx-auto">
            {/* Progress Header */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <h2 className="text-lg font-semibold">Review Session Progress</h2>
                <span className="text-sm text-gray-600">
                  {currentItemIndex + 1} of {reviewItems.length} items
                </span>
              </div>
              <Progress value={getProgress()} className="h-2" />
            </div>

            {/* Current Review Item */}
            {getCurrentItem() && (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl">
                        {getCurrentItem().lesson.title}
                      </CardTitle>
                      <CardDescription>
                        {getCurrentItem().lesson.module.title} • {getCurrentItem().lesson.module.type}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getItemTypeColor(getCurrentItem().itemType)}>
                        {getCurrentItem().itemType.replace('_', ' ')}
                      </Badge>
                      <Badge variant="outline">
                        Difficulty: {Math.round(getCurrentItem().difficulty * 100)}%
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ReviewItemComponent
                    item={getCurrentItem()}
                    onSubmit={handleAnswerSubmit}
                    isSubmitting={isSubmitting}
                  />
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

interface ReviewItemComponentProps {
  item: ReviewItem
  onSubmit: (reviewItemId: string, answer: string, score: number) => void
  isSubmitting: boolean
}

function ReviewItemComponent({ item, onSubmit, isSubmitting }: ReviewItemComponentProps) {
  const [userAnswer, setUserAnswer] = useState("")
  const [showResult, setShowResult] = useState(false)

  const lessonContent = JSON.parse(item.lesson.content)
  const lessonExercises = JSON.parse(item.lesson.exercises)

  const handleSubmit = () => {
    // Simple scoring logic - can be enhanced based on item type
    const isCorrect = userAnswer.toLowerCase().trim() === "correct" // Simplified for demo
    const score = isCorrect ? 100 : 0
    
    onSubmit(item.id, userAnswer, score)
    setShowResult(true)
  }

  return (
    <div className="space-y-6">
      {/* Content Review */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium mb-2">Quick Review:</h4>
        {lessonContent.explanations?.[0] && (
          <div className="space-y-2">
            <h5 className="font-medium text-sm">{lessonContent.explanations[0].title}</h5>
            <p className="text-sm text-gray-700">{lessonContent.explanations[0].content}</p>
          </div>
        )}
      </div>

      {/* Exercise */}
      <div className="space-y-4">
        <h4 className="font-medium">Test your knowledge:</h4>
        
        {lessonExercises.vocabulary?.[0] && (
          <div className="border rounded-lg p-4">
            <p className="font-medium text-lg mb-2">{lessonExercises.vocabulary[0].word}</p>
            <p className="text-sm text-gray-600 mb-3">{lessonExercises.vocabulary[0].reading}</p>
            <Input
              placeholder="Enter the meaning"
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              disabled={showResult}
            />
          </div>
        )}

        {showResult && (
          <div className={`p-4 rounded-lg border ${
            userAnswer.toLowerCase() === "correct" 
              ? "bg-green-50 border-green-200" 
              : "bg-red-50 border-red-200"
          }`}>
            <div className="flex items-center space-x-2">
              {userAnswer.toLowerCase() === "correct" ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <XCircle className="w-5 h-5 text-red-600" />
              )}
              <span className="font-medium">
                {userAnswer.toLowerCase() === "correct" ? "Correct!" : "Try again!"}
              </span>
            </div>
          </div>
        )}

        <Button 
          onClick={handleSubmit} 
          disabled={!userAnswer || isSubmitting || showResult}
          className="w-full"
        >
          {isSubmitting ? "Submitting..." : "Submit Answer"}
        </Button>
      </div>
    </div>
  )
}