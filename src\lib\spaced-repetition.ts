import { ReviewItemType, UserProgress } from "@prisma/client"

export interface SpacedRepetitionItem {
  id: string
  type: ReviewItemType
  difficulty: number // 0-1, where 1 is most difficult
  interval: number // days until next review
  easeFactor: number // typically 2.5
  nextReview: Date
  consecutiveCorrect: number
  consecutiveIncorrect: number
}

export class SpacedRepetitionScheduler {
  /**
   * Calculate the next review interval using the SM-2 algorithm
   * Based on user performance and previous intervals
   */
  static calculateNextReview(
    currentInterval: number,
    easeFactor: number,
    quality: number // 0-5, where 5 is perfect response
  ): { interval: number; easeFactor: number } {
    // SM-2 algorithm implementation
    if (quality < 3) {
      // Reset interval for poor performance
      return { interval: 1, easeFactor: Math.max(1.3, easeFactor - 0.2) }
    }

    // Calculate new interval
    let newInterval: number
    if (currentInterval === 0) {
      newInterval = 1
    } else if (currentInterval === 1) {
      newInterval = 6
    } else {
      newInterval = Math.round(currentInterval * easeFactor)
    }

    // Update ease factor
    const newEaseFactor = Math.max(1.3, easeFactor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02)))

    return { interval: newInterval, easeFactor: newEaseFactor }
  }

  /**
   * Convert lesson score to quality rating (0-5)
   */
  static scoreToQuality(score: number): number {
    if (score >= 95) return 5 // Perfect response
    if (score >= 85) return 4 // Correct response after a hesitation
    if (score >= 70) return 3 // Correct response recalled with serious difficulty
    if (score >= 50) return 2 // Incorrect response; the correct one remembered
    return 1 // Incorrect response; the correct one seemed easy to recall
  }

  /**
   * Generate review items from lesson history and progress
   */
  static generateReviewItems(
    lessonHistory: Array<{
      lessonId: string
      score: number
      completedAt: Date
      answers: string
    }>,
    existingReviewItems: Array<{
      lessonId: string
      itemType: ReviewItemType
      difficulty: number
      interval: number
      easeFactor: number
      nextReview: Date
    }>
  ): SpacedRepetitionItem[] {
    const reviewItems: SpacedRepetitionItem[] = []
    const now = new Date()

    // Process recent lesson history
    lessonHistory.forEach(history => {
      const quality = this.scoreToQuality(history.score)
      const existingItem = existingReviewItems.find(item => item.lessonId === history.lessonId)

      if (existingItem) {
        // Update existing item
        const { interval, easeFactor } = this.calculateNextReview(
          existingItem.interval,
          existingItem.easeFactor,
          quality
        )

        // Update difficulty based on performance
        const newDifficulty = Math.max(0, Math.min(1, 
          existingItem.difficulty + (quality < 3 ? 0.1 : -0.05)
        ))

        reviewItems.push({
          id: existingItem.lessonId,
          type: this.determineReviewType(history.answers),
          difficulty: newDifficulty,
          interval,
          easeFactor,
          nextReview: new Date(now.getTime() + interval * 24 * 60 * 60 * 1000),
          consecutiveCorrect: quality >= 3 ? (existingItem.consecutiveCorrect || 0) + 1 : 0,
          consecutiveIncorrect: quality < 3 ? (existingItem.consecutiveIncorrect || 0) + 1 : 0
        })
      } else {
        // Create new review item
        const { interval, easeFactor } = this.calculateNextReview(0, 2.5, quality)
        
        reviewItems.push({
          id: history.lessonId,
          type: this.determineReviewType(history.answers),
          difficulty: Math.max(0, Math.min(1, 1 - (score / 100))),
          interval,
          easeFactor,
          nextReview: new Date(now.getTime() + interval * 24 * 60 * 60 * 1000),
          consecutiveCorrect: quality >= 3 ? 1 : 0,
          consecutiveIncorrect: quality < 3 ? 1 : 0
        })
      }
    })

    return reviewItems.sort((a, b) => a.nextReview.getTime() - b.nextReview.getTime())
  }

  /**
   * Determine review item type based on lesson answers
   */
  private static determineReviewType(answersJson: string): ReviewItemType {
    try {
      const answers = JSON.parse(answersJson)
      
      // Check if answers contain vocabulary items
      if (answers.some((a: any) => a.exerciseType === "vocabulary")) {
        return ReviewItemType.VOCABULARY
      }
      
      // Check if answers contain grammar items
      if (answers.some((a: any) => a.exerciseType === "grammar")) {
        return ReviewItemType.GRAMMAR_POINT
      }
      
      // Check if answers contain kanji items
      if (answers.some((a: any) => a.exerciseType === "kanji")) {
        return ReviewItemType.KANJI_CHARACTER
      }
      
      return ReviewItemType.SENTENCE
    } catch {
      return ReviewItemType.SENTENCE
    }
  }

  /**
   * Get items due for review
   */
  static getDueReviewItems(reviewItems: SpacedRepetitionItem[]): SpacedRepetitionItem[] {
    const now = new Date()
    return reviewItems.filter(item => item.nextReview <= now)
  }

  /**
   * Get upcoming review items (next 7 days)
   */
  static getUpcomingReviewItems(reviewItems: SpacedRepetitionItem[]): SpacedRepetitionItem[] {
    const now = new Date()
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    return reviewItems.filter(item => 
      item.nextReview > now && item.nextReview <= nextWeek
    ).sort((a, b) => a.nextReview.getTime() - b.nextReview.getTime())
  }

  /**
   * Calculate optimal review session size based on user's time commitment
   */
  static calculateOptimalSessionSize(
    timeCommitmentMinutes: number,
    averageItemTimeMinutes: number = 2
  ): number {
    // Use 80% of available time for reviews, leave 20% for new content
    const reviewTime = timeCommitmentMinutes * 0.8
    return Math.max(1, Math.floor(reviewTime / averageItemTimeMinutes))
  }

  /**
   * Prioritize review items based on urgency and difficulty
   */
  static prioritizeReviewItems(items: SpacedRepetitionItem[]): SpacedRepetitionItem[] {
    return items.sort((a, b) => {
      // First priority: overdue items
      const now = new Date()
      const aOverdue = now.getTime() - a.nextReview.getTime()
      const bOverdue = now.getTime() - b.nextReview.getTime()
      
      if (aOverdue > 0 && bOverdue <= 0) return -1
      if (aOverdue <= 0 && bOverdue > 0) return 1
      
      // Second priority: difficulty (higher difficulty = higher priority)
      if (a.difficulty !== b.difficulty) {
        return b.difficulty - a.difficulty
      }
      
      // Third priority: consecutive incorrect answers
      if (a.consecutiveIncorrect !== b.consecutiveIncorrect) {
        return b.consecutiveIncorrect - a.consecutiveIncorrect
      }
      
      // Finally: sort by next review time
      return a.nextReview.getTime() - b.nextReview.getTime()
    })
  }

  /**
   * Generate review statistics
   */
  static generateReviewStats(reviewItems: SpacedRepetitionItem[]): {
    totalItems: number
    dueItems: number
    upcomingItems: number
    averageDifficulty: number
    averageInterval: number
  } {
    const now = new Date()
    const dueItems = reviewItems.filter(item => item.nextReview <= now)
    const upcomingItems = reviewItems.filter(item => item.nextReview > now)
    
    const totalDifficulty = reviewItems.reduce((sum, item) => sum + item.difficulty, 0)
    const totalInterval = reviewItems.reduce((sum, item) => sum + item.interval, 0)
    
    return {
      totalItems: reviewItems.length,
      dueItems: dueItems.length,
      upcomingItems: upcomingItems.length,
      averageDifficulty: reviewItems.length > 0 ? totalDifficulty / reviewItems.length : 0,
      averageInterval: reviewItems.length > 0 ? totalInterval / reviewItems.length : 0
    }
  }
}