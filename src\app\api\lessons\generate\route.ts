import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import ZAI from 'z-ai-web-dev-sdk'
import { ModuleType } from "@prisma/client"

interface LessonContent {
  explanations: Array<{
    title: string
    content: string
    examples?: Array<{
      japanese: string
      reading: string
      meaning: string
    }>
  }>
  culturalNotes?: Array<{
    title: string
    content: string
  }>
}

interface LessonExercises {
  vocabulary?: Array<{
    word: string
    reading: string
    meaning: string
    type: string
  }>
  grammar?: Array<{
    pattern: string
    meaning: string
    examples: Array<{
      japanese: string
      reading: string
      meaning: string
    }>
    practice: Array<{
      question: string
      answer: string
    }>
  }>
  kanji?: Array<{
    character: string
    meaning: string
    readings: Array<{
      type: string
      reading: string
    }>
    strokeCount: number
    examples: Array<{
      word: string
      reading: string
      meaning: string
    }>
  }>
  matching?: Array<{
    question: string
    options: string[]
    correctAnswer: string
  }>
  fillInBlanks?: Array<{
    sentence: string
    answer: string
    options?: string[]
  }>
}

interface GeneratedLesson {
  title: string
  description: string
  content: LessonContent
  exercises: LessonExercises
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { moduleId, moduleTitle, moduleType, moduleDescription, order } = await request.json()

    if (!moduleId || !moduleTitle || !moduleType || !order) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Get user info and roadmap context
    const user = await db.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get module with its roadmap for context
    const moduleData = await db.module.findUnique({
      where: { id: moduleId },
      include: {
        roadmap: {
          include: {
            modules: {
              where: { order: { lt: order } },
              orderBy: { order: 'desc' },
              take: 1 // Get previous module for context
            }
          }
        }
      }
    })

    if (!moduleData) {
      return NextResponse.json({ error: "Module not found" }, { status: 404 })
    }

    // Initialize ZAI SDK
    const zai = await ZAI.create()

    // Generate lesson using AI
    const prompt = `
    You are an expert Japanese language instructor. Create a comprehensive lesson for a specific module.

    Context:
    - Student Level: ${user.level}
    - Module Type: ${moduleType}
    - Module Title: ${moduleTitle}
    - Module Description: ${moduleDescription}
    - Student Goals: ${user.learningGoals}
    - Daily Time Commitment: ${user.timeCommitment} minutes
    - Previous Module: ${moduleData.roadmap.modules[0]?.title || "None (first module)"}

    Create a lesson that is appropriate for the student's level and the module type.
    The lesson should be engaging, educational, and include practical examples.

    For ${moduleType} modules, focus on:
    ${getModuleFocus(moduleType)}

    Return the lesson in this JSON format:
    {
      "title": "Lesson title",
      "description": "Brief lesson description",
      "content": {
        "explanations": [
          {
            "title": "Explanation title",
            "content": "Detailed explanation",
            "examples": [
              {
                "japanese": "Japanese text",
                "reading": "Reading pronunciation",
                "meaning": "English meaning"
              }
            ]
          }
        ],
        "culturalNotes": [
          {
            "title": "Cultural note title",
            "content": "Cultural explanation"
          }
        ]
      },
      "exercises": {
        ${getExerciseStructure(moduleType)}
      }
    }

    Ensure the content is accurate, pedagogically sound, and appropriate for the student's level.
    Include furigana for difficult kanji in examples.
    Make exercises challenging but achievable.
    `

    const completion = await zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert Japanese language instructor specializing in creating engaging, effective lessons.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 3000
    })

    let lessonData: GeneratedLesson
    try {
      const content = completion.choices[0]?.message?.content
      if (!content) {
        throw new Error("No content received from AI")
      }
      
      // Extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error("No JSON found in AI response")
      }
      
      lessonData = JSON.parse(jsonMatch[0])
    } catch (error) {
      console.error("Error parsing AI response:", error)
      return NextResponse.json({ error: "Failed to generate lesson" }, { status: 500 })
    }

    // Create lesson in database
    const lesson = await db.lesson.create({
      data: {
        title: lessonData.title,
        description: lessonData.description,
        content: JSON.stringify(lessonData.content),
        exercises: JSON.stringify(lessonData.exercises),
        order: order,
        moduleId: moduleId
      }
    })

    return NextResponse.json({ lesson })
  } catch (error) {
    console.error("Lesson generation error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

function getModuleFocus(moduleType: ModuleType): string {
  const focusMap = {
    HIRAGANA: "Hiragana characters, stroke order, pronunciation, and basic words. Include visual mnemonics and writing practice.",
    KATAKANA: "Katakana characters, stroke order, pronunciation, and loanwords. Include visual mnemonics and writing practice.",
    VOCABULARY: "Thematic vocabulary with readings, meanings, and example sentences. Focus on practical, high-frequency words.",
    GRAMMAR: "Grammar patterns, usage rules, and sentence structure. Include many examples and common mistakes.",
    KANJI: "Kanji characters, meanings, readings, stroke order, and compound words. Include mnemonics and writing practice.",
    CONVERSATION: "Dialogues, common phrases, and speaking practice. Focus on natural, everyday communication.",
    LISTENING: "Listening comprehension, audio-based exercises, and pronunciation practice.",
    READING: "Reading comprehension, short texts, and understanding written Japanese.",
    WRITING: "Writing practice, composition, and expressing ideas in written Japanese.",
    REVIEW: "Comprehensive review of previous topics with mixed exercises."
  }
  return focusMap[moduleType] || "General Japanese language learning."
}

function getExerciseStructure(moduleType: ModuleType): string {
  const structureMap = {
    HIRAGANA: `
      "vocabulary": [
        {
          "word": "hiragana character",
          "reading": "pronunciation",
          "meaning": "romaji/meaning",
          "type": "hiragana"
        }
      ],
      "matching": [
        {
          "question": "hiragana character",
          "options": ["romaji1", "romaji2", "romaji3", "romaji4"],
          "correctAnswer": "correct romaji"
        }
      ]`,
    KATAKANA: `
      "vocabulary": [
        {
          "word": "katakana character",
          "reading": "pronunciation",
          "meaning": "romaji/meaning",
          "type": "katakana"
        }
      ],
      "matching": [
        {
          "question": "katakana character",
          "options": ["romaji1", "romaji2", "romaji3", "romaji4"],
          "correctAnswer": "correct romaji"
        }
      ]`,
    VOCABULARY: `
      "vocabulary": [
        {
          "word": "japanese word",
          "reading": "reading",
          "meaning": "english meaning",
          "type": "part of speech"
        }
      ],
      "matching": [
        {
          "question": "japanese word",
          "options": ["meaning1", "meaning2", "meaning3", "meaning4"],
          "correctAnswer": "correct meaning"
        }
      ],
      "fillInBlanks": [
        {
          "sentence": "sentence with blank",
          "answer": "correct word",
          "options": ["word1", "word2", "word3", "word4"]
        }
      ]`,
    GRAMMAR: `
      "grammar": [
        {
          "pattern": "grammar pattern",
          "meaning": "meaning/usage",
          "examples": [
            {
              "japanese": "example sentence",
              "reading": "reading",
              "meaning": "meaning"
            }
          ],
          "practice": [
            {
              "question": "practice question",
              "answer": "model answer"
            }
          ]
        },
        "fillInBlanks": [
          {
            "sentence": "sentence with blank",
            "answer": "correct grammar form",
            "options": ["option1", "option2", "option3", "option4"]
          }
        ]`,
    KANJI: `
      "kanji": [
        {
          "character": "kanji character",
          "meaning": "meaning",
          "readings": [
            {
              "type": "kun'yomi",
              "reading": "reading"
            }
          ],
          "strokeCount": number,
          "examples": [
            {
              "word": "compound word",
              "reading": "reading",
              "meaning": "meaning"
            }
          ]
        }
      ],
      "matching": [
        {
          "question": "kanji character",
          "options": ["meaning1", "meaning2", "meaning3", "meaning4"],
          "correctAnswer": "correct meaning"
        }
      ]`,
    CONVERSATION: `
      "vocabulary": [
        {
          "word": "phrase/expression",
          "reading": "reading",
          "meaning": "meaning",
          "type": "conversation"
        }
      ],
      "fillInBlanks": [
        {
          "sentence": "dialogue with blank",
          "answer": "correct phrase",
          "options": ["phrase1", "phrase2", "phrase3", "phrase4"]
        }
      ]`,
    default: `
      "vocabulary": [
        {
          "word": "japanese word",
          "reading": "reading",
          "meaning": "english meaning",
          "type": "general"
        }
      ],
      "matching": [
        {
          "question": "question",
          "options": ["option1", "option2", "option3", "option4"],
          "correctAnswer": "correct answer"
        }
      ]`
  }
  return structureMap[moduleType] || structureMap.default
}