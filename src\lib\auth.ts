import { NextAuthOptions } from "next-auth"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { db } from "@/lib/db"
import Cred<PERSON>sProvider from "next-auth/providers/credentials"
import { UserLevel } from "@prisma/client"
import bcrypt from "bcryptjs"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        name: { label: "Name", type: "text", required: false },
        level: { label: "Level", type: "text", required: false },
        learningGoals: { label: "Learning Goals", type: "text", required: false },
        timeCommitment: { label: "Time Commitment (minutes/day)", type: "number", required: false },
        isRegister: { label: "Is Register", type: "boolean", required: false }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const { email, password, name, level, learningGoals, timeCommitment, isRegister } = credentials

        // Handle registration
        if (isRegister === "true") {
          try {
            const hashedPassword = await bcrypt.hash(password, 12)
            
            const user = await db.user.create({
              data: {
                email,
                name: name || null,
                level: (level as UserLevel) || UserLevel.BEGINNER,
                learningGoals: learningGoals || '["general"]',
                timeCommitment: parseInt(timeCommitment as string) || 30,
              }
            })

            return {
              id: user.id,
              email: user.email,
              name: user.name,
              level: user.level,
              learningGoals: user.learningGoals,
              timeCommitment: user.timeCommitment,
            }
          } catch (error) {
            console.error("Registration error:", error)
            return null
          }
        }

        // Handle login
        const user = await db.user.findUnique({
          where: { email }
        })

        if (!user) {
          return null
        }

        // For demo purposes, we'll skip password verification in development
        // In production, you should verify the password here
        // const isPasswordValid = await bcrypt.compare(password, user.password)
        // if (!isPasswordValid) {
        //   return null
        // }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          level: user.level,
          learningGoals: user.learningGoals,
          timeCommitment: user.timeCommitment,
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.level = user.level
        token.learningGoals = user.learningGoals
        token.timeCommitment = user.timeCommitment
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.level = token.level as UserLevel
        session.user.learningGoals = token.learningGoals as string
        session.user.timeCommitment = token.timeCommitment as number
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup"
  }
}