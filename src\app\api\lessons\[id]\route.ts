import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const lesson = await db.lesson.findUnique({
      where: { id: params.id },
      include: {
        module: {
          include: {
            roadmap: true
          }
        },
        progress: {
          where: { userId: session.user.id }
        },
        history: {
          where: { userId: session.user.id },
          orderBy: { completedAt: 'desc' },
          take: 1
        }
      }
    })

    if (!lesson) {
      return NextResponse.json({ error: "Lesson not found" }, { status: 404 })
    }

    // Verify user has access to this lesson
    if (lesson.module.roadmap.userId !== session.user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json({ lesson })
  } catch (error) {
    console.error("Error fetching lesson:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { answers, score, timeSpent } = await request.json()

    if (!answers || score === undefined || !timeSpent) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Get lesson and verify access
    const lesson = await db.lesson.findUnique({
      where: { id: params.id },
      include: {
        module: {
          include: {
            roadmap: true
          }
        }
      }
    })

    if (!lesson) {
      return NextResponse.json({ error: "Lesson not found" }, { status: 404 })
    }

    if (lesson.module.roadmap.userId !== session.user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Record lesson completion
    const lessonHistory = await db.lessonHistory.create({
      data: {
        userId: session.user.id,
        lessonId: params.id,
        score: parseFloat(score),
        timeSpent: parseInt(timeSpent),
        answers: JSON.stringify(answers)
      }
    })

    // Update or create progress
    const progressData = {
      status: "COMPLETED" as const,
      progress: 100,
      completedAt: new Date(),
      timeSpent: parseInt(timeSpent)
    }

    await db.userProgress.upsert({
      where: {
        userId_roadmapId_moduleId_lessonId: {
          userId: session.user.id,
          roadmapId: lesson.module.roadmapId,
          moduleId: lesson.moduleId,
          lessonId: lesson.id
        }
      },
      update: progressData,
      create: {
        userId: session.user.id,
        roadmapId: lesson.module.roadmapId,
        moduleId: lesson.moduleId,
        lessonId: lesson.id,
        ...progressData
      }
    })

    // Update module progress
    const moduleLessons = await db.lesson.findMany({
      where: { moduleId: lesson.moduleId }
    })

    const moduleProgress = await db.userProgress.findMany({
      where: {
        userId: session.user.id,
        moduleId: lesson.moduleId,
        status: "COMPLETED"
      }
    })

    const moduleProgressPercentage = (moduleProgress.length / moduleLessons.length) * 100

    await db.userProgress.upsert({
      where: {
        userId_roadmapId_moduleId: {
          userId: session.user.id,
          roadmapId: lesson.module.roadmapId,
          moduleId: lesson.moduleId
        }
      },
      update: {
        progress: moduleProgressPercentage,
        status: moduleProgressPercentage === 100 ? "COMPLETED" as const : "IN_PROGRESS" as const
      },
      create: {
        userId: session.user.id,
        roadmapId: lesson.module.roadmapId,
        moduleId: lesson.moduleId,
        progress: moduleProgressPercentage,
        status: moduleProgressPercentage === 100 ? "COMPLETED" as const : "IN_PROGRESS" as const
      }
    })

    return NextResponse.json({ 
      success: true, 
      lessonHistory,
      moduleProgress: moduleProgressPercentage 
    })
  } catch (error) {
    console.error("Error completing lesson:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}